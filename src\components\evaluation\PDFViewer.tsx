import React, { useState, useRef, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import * as fabric from 'fabric';

import { 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Pencil, 
  Type, 
  Square,
  Circle,
  Minus,
  Save,
  Download
} from 'lucide-react';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface PDFViewerProps {
  pdfUrl: string;
  onAnnotationChange: (annotations: any[]) => void;
  annotations: any[];
}

const PDFViewer: React.FC<PDFViewerProps> = ({ pdfUrl, onAnnotationChange, annotations }) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  useEffect(() => {
    if (canvasRef.current && !fabricCanvasRef.current) {
      fabricCanvasRef.current = new fabric.Canvas(canvasRef.current, {
        isDrawingMode: false,
        selection: true,
        backgroundColor: 'transparent'
      });

      // Set up drawing brush
      fabricCanvasRef.current.freeDrawingBrush.width = 2;
      fabricCanvasRef.current.freeDrawingBrush.color = '#ff0000';

      // Listen for object modifications
      fabricCanvasRef.current.on('object:modified', saveAnnotations);
      fabricCanvasRef.current.on('path:created', saveAnnotations);
      fabricCanvasRef.current.on('object:added', saveAnnotations);
    }

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, []);

  const saveAnnotations = () => {
    if (fabricCanvasRef.current) {
      const objects = fabricCanvasRef.current.getObjects();
      const annotationData = objects.map(obj => ({
        type: obj.type,
        data: obj.toObject(),
        page: pageNumber
      }));
      onAnnotationChange(annotationData);
    }
  };

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const changePage = (offset: number) => {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      return Math.min(Math.max(1, newPageNumber), numPages);
    });
  };

  const changeScale = (newScale: number) => {
    setScale(Math.min(Math.max(0.5, newScale), 3.0));
  };

  const selectTool = (tool: string) => {
    setSelectedTool(tool);
    if (fabricCanvasRef.current) {
      switch (tool) {
        case 'draw':
          fabricCanvasRef.current.isDrawingMode = true;
          fabricCanvasRef.current.selection = false;
          break;
        case 'select':
          fabricCanvasRef.current.isDrawingMode = false;
          fabricCanvasRef.current.selection = true;
          break;
        default:
          fabricCanvasRef.current.isDrawingMode = false;
          fabricCanvasRef.current.selection = false;
      }
    }
  };

  const addShape = (shapeType: string) => {
    if (!fabricCanvasRef.current) return;

    let shape;
    switch (shapeType) {
      case 'rectangle':
        shape = new fabric.Rect({
          left: 100,
          top: 100,
          width: 100,
          height: 60,
          fill: 'transparent',
          stroke: '#ff0000',
          strokeWidth: 2
        });
        break;
      case 'circle':
        shape = new fabric.Circle({
          left: 100,
          top: 100,
          radius: 50,
          fill: 'transparent',
          stroke: '#ff0000',
          strokeWidth: 2
        });
        break;
      case 'line':
        shape = new fabric.Line([50, 100, 200, 100], {
          stroke: '#ff0000',
          strokeWidth: 2
        });
        break;
    }

    if (shape) {
      fabricCanvasRef.current.add(shape);
      fabricCanvasRef.current.setActiveObject(shape);
    }
  };

  const addText = () => {
    if (!fabricCanvasRef.current) return;

    const text = new fabric.IText('Click to edit', {
      left: 100,
      top: 100,
      fontFamily: 'Arial',
      fontSize: 16,
      fill: '#ff0000'
    });

    fabricCanvasRef.current.add(text);
    fabricCanvasRef.current.setActiveObject(text);
  };

  const clearAnnotations = () => {
    if (fabricCanvasRef.current) {
      fabricCanvasRef.current.clear();
      onAnnotationChange([]);
    }
  };

  const exportAnnotatedPDF = async () => {
    // This would integrate with a backend service to merge annotations with PDF
    console.log('Exporting annotated PDF...');
  };

  return (
    <div className="flex flex-col h-full bg-gray-100">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-2">
          {/* Tool Selection */}
          <div className="flex items-center space-x-1 border-r border-gray-300 pr-3">
            <button
              onClick={() => selectTool('select')}
              className={`p-2 rounded ${selectedTool === 'select' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}
              title="Select"
            >
              <span className="text-sm">↖</span>
            </button>
            <button
              onClick={() => selectTool('draw')}
              className={`p-2 rounded ${selectedTool === 'draw' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}
              title="Draw"
            >
              <Pencil className="w-4 h-4" />
            </button>
            <button
              onClick={addText}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              title="Add Text"
            >
              <Type className="w-4 h-4" />
            </button>
            <button
              onClick={() => addShape('rectangle')}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              title="Rectangle"
            >
              <Square className="w-4 h-4" />
            </button>
            <button
              onClick={() => addShape('circle')}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              title="Circle"
            >
              <Circle className="w-4 h-4" />
            </button>
            <button
              onClick={() => addShape('line')}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              title="Line"
            >
              <Minus className="w-4 h-4" />
            </button>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => changeScale(scale - 0.25)}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              disabled={scale <= 0.5}
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-sm text-gray-600 min-w-16 text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={() => changeScale(scale + 0.25)}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
              disabled={scale >= 3.0}
            >
              <ZoomIn className="w-4 h-4" />
            </button>
            <button
              onClick={() => changeScale(1.0)}
              className="p-2 rounded text-gray-600 hover:bg-gray-100"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Page Navigation */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => changePage(-1)}
            disabled={pageNumber <= 1}
            className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {pageNumber} of {numPages}
          </span>
          <button
            onClick={() => changePage(1)}
            disabled={pageNumber >= numPages}
            className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={clearAnnotations}
            className="px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50"
          >
            Clear
          </button>
          <button
            onClick={exportAnnotatedPDF}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-1"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* PDF Viewer with Overlay Canvas */}
      <div className="flex-1 overflow-auto p-4">
        <div className="relative inline-block">
          <Document
            file={pdfUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            className="shadow-lg"
          >
            <Page 
              pageNumber={pageNumber} 
              scale={scale}
              className="relative"
            />
          </Document>
          <canvas
            ref={canvasRef}
            className="absolute top-0 left-0 pointer-events-auto"
            style={{
              width: `${595 * scale}px`,
              height: `${842 * scale}px`
            }}
            width={595 * scale}
            height={842 * scale}
          />
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;