import React from 'react';
import { AuthProvider, useAuth } from './context/AuthContext';
import Layout from './components/common/Layout';
import LoginForm from './components/auth/LoginForm';
import AdminDashboard from './components/dashboard/AdminDashboard';
import EvaluatorDashboard from './components/dashboard/EvaluatorDashboard';
import StudentDashboard from './components/dashboard/StudentDashboard';
import EvaluationInterface from './components/evaluation/EvaluationInterface';
import PaperManagement from './components/papers/PaperManagement';
import ExamManagement from './components/exams/ExamManagement';
import UserManagement from './components/users/UserManagement';
import AuditDashboard from './components/audit/AuditDashboard';
import ReportsPage from './components/reports/ReportsPage';

function AppContent() {
  const { user, isLoading } = useAuth();
  const [currentView, setCurrentView] = React.useState<string>('dashboard');

  // Simple routing based on URL hash for demo purposes
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) || 'dashboard';
      setCurrentView(hash);
    };

    handleHashChange();
    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return <LoginForm />;
  }

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        if (user.role === 'admin') return <AdminDashboard />;
        if (user.role === 'evaluator') return <EvaluatorDashboard />;
        if (user.role === 'student') return <StudentDashboard />;
        if (user.role === 'auditor') return <AuditDashboard />;
        return <AdminDashboard />;
      
      case 'evaluate':
        return <EvaluationInterface onBack={() => setCurrentView('dashboard')} />;
      
      case 'papers':
        return <PaperManagement />;
      
      case 'exams':
        return <ExamManagement />;
      
      case 'users':
        return <UserManagement />;
      
      case 'audit':
        return <AuditDashboard />;
      
      case 'reports':
        return <ReportsPage />;
      
      case 'my-papers':
        if (user.role === 'evaluator') {
          return <EvaluatorDashboard />;
        }
        return <PaperManagement />;
      
      case 'results':
        return <StudentDashboard />;
      
      default:
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {currentView.charAt(0).toUpperCase() + currentView.slice(1)} Module
            </h2>
            <p className="text-gray-600">
              This module is under development. Please check back later.
            </p>
          </div>
        );
    }
  };

  return (
    <Layout>
      {renderContent()}
    </Layout>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;