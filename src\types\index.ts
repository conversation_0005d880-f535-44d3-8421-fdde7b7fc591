export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'evaluator' | 'auditor' | 'student';
  department?: string;
  isActive: boolean;
  createdAt: string;
}

export interface Exam {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  subjects: string[];
  createdBy: string;
  status: 'draft' | 'active' | 'completed';
  totalPapers: number;
}

export interface Paper {
  id: string;
  examId: string;
  studentId: string;
  studentName: string;
  subject: string;
  uploadUrl: string;
  status: 'pending' | 'assigned' | 'evaluating' | 'evaluated' | 'under-audit' | 'completed';
  assignedTo?: string;
  assignedEvaluator?: string;
  dateUploaded: string;
  dateAssigned?: string;
  dateEvaluated?: string;
  totalMarks?: number;
  pageCount: number;
}

export interface Evaluation {
  id: string;
  paperId: string;
  evaluatorId: string;
  dateStarted: string;
  dateSubmitted?: string;
  finalScore?: number;
  status: 'in-progress' | 'submitted' | 'under-audit' | 'approved';
  answers: AnswerMark[];
  annotations: Annotation[];
}

export interface AnswerMark {
  id: string;
  questionNo: number;
  marksGiven: number;
  maxMarks: number;
  comments: string;
}

export interface Annotation {
  id: string;
  type: 'highlight' | 'text' | 'drawing';
  x: number;
  y: number;
  width?: number;
  height?: number;
  content: string;
  color: string;
}

export interface AuditLog {
  id: string;
  action: string;
  entity: string;
  entityId: string;
  userId: string;
  userName: string;
  timestamp: string;
  details: string;
}

export interface DashboardStats {
  totalPapers: number;
  completedPapers: number;
  pendingPapers: number;
  averageScore: number;
  evaluationSpeed: number;
}