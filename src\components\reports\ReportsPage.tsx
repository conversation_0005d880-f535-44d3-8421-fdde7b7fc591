import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  Filter,
  FileText,
  Users,
  Clock,
  Target
} from 'lucide-react';

const ReportsPage: React.FC = () => {
  const [dateRange, setDateRange] = useState('last-30-days');
  const [reportType, setReportType] = useState('overview');

  const reportData = {
    overview: {
      totalPapers: 1250,
      completedPapers: 1100,
      pendingPapers: 150,
      averageScore: 78.5,
      averageTimePerPaper: 2.3,
      evaluatorCount: 25,
      subjectCount: 12
    },
    evaluatorPerformance: [
      { name: 'Dr. <PERSON>', papers: 45, avgTime: 1.8, avgScore: 76.8, accuracy: 94 },
      { name: 'Prof. <PERSON>', papers: 38, avgTime: 2.1, avgScore: 79.2, accuracy: 96 },
      { name: 'Dr. <PERSON>', papers: 42, avgTime: 2.0, avgScore: 77.5, accuracy: 92 },
      { name: 'Prof<PERSON> <PERSON>', papers: 35, avgTime: 2.5, avgScore: 80.1, accuracy: 95 }
    ],
    subjectAnalysis: [
      { subject: 'Data Structures', papers: 180, avgScore: 75.2, passRate: 85 },
      { subject: 'Algorithms', papers: 165, avgScore: 78.9, passRate: 88 },
      { subject: 'Database Systems', papers: 145, avgScore: 82.1, passRate: 92 },
      { subject: 'Computer Networks', papers: 120, avgScore: 74.8, passRate: 82 }
    ]
  };

  const exportReport = (format: string) => {
    console.log(`Exporting report in ${format} format`);
    // Here you would implement actual export functionality
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex items-center space-x-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="last-7-days">Last 7 days</option>
            <option value="last-30-days">Last 30 days</option>
            <option value="last-90-days">Last 90 days</option>
            <option value="custom">Custom Range</option>
          </select>
          <button
            onClick={() => exportReport('pdf')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'evaluators', label: 'Evaluator Performance', icon: Users },
              { id: 'subjects', label: 'Subject Analysis', icon: FileText },
              { id: 'trends', label: 'Trends', icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setReportType(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  reportType === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {reportType === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-600">Total Papers</p>
                      <p className="text-3xl font-bold text-blue-900 mt-2">
                        {reportData.overview.totalPapers.toLocaleString()}
                      </p>
                    </div>
                    <FileText className="w-8 h-8 text-blue-600" />
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-600">Completed</p>
                      <p className="text-3xl font-bold text-green-900 mt-2">
                        {reportData.overview.completedPapers.toLocaleString()}
                      </p>
                    </div>
                    <Target className="w-8 h-8 text-green-600" />
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-yellow-600">Average Score</p>
                      <p className="text-3xl font-bold text-yellow-900 mt-2">
                        {reportData.overview.averageScore}%
                      </p>
                    </div>
                    <BarChart3 className="w-8 h-8 text-yellow-600" />
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-600">Avg Time/Paper</p>
                      <p className="text-3xl font-bold text-purple-900 mt-2">
                        {reportData.overview.averageTimePerPaper}h
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-purple-600" />
                  </div>
                </div>
              </div>

              {/* Charts Placeholder */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-6 h-64 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                    <p>Papers Completed Over Time</p>
                    <p className="text-sm">(Chart would be rendered here)</p>
                  </div>
                </div>
                <div className="bg-gray-50 rounded-lg p-6 h-64 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <TrendingUp className="w-12 h-12 mx-auto mb-2" />
                    <p>Score Distribution</p>
                    <p className="text-sm">(Chart would be rendered here)</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {reportType === 'evaluators' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Evaluator Performance</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Evaluator
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Papers Evaluated
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg Time (hours)
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg Score Given
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Accuracy
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportData.evaluatorPerformance.map((evaluator, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {evaluator.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {evaluator.papers}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {evaluator.avgTime}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {evaluator.avgScore}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full" 
                                style={{ width: `${evaluator.accuracy}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-900">{evaluator.accuracy}%</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {reportType === 'subjects' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Subject Analysis</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {reportData.subjectAnalysis.map((subject, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-lg font-medium text-gray-900 mb-4">{subject.subject}</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Papers Evaluated</span>
                        <span className="text-sm font-medium text-gray-900">{subject.papers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Average Score</span>
                        <span className="text-sm font-medium text-gray-900">{subject.avgScore}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Pass Rate</span>
                        <span className="text-sm font-medium text-gray-900">{subject.passRate}%</span>
                      </div>
                      <div className="mt-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Performance</span>
                          <span className="text-gray-900">{subject.passRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full" 
                            style={{ width: `${subject.passRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {reportType === 'trends' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Evaluation Trends</h3>
              <div className="bg-gray-50 rounded-lg p-12 text-center">
                <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Trend Analysis</h4>
                <p className="text-gray-600">
                  Interactive charts showing evaluation trends over time would be displayed here.
                  This would include metrics like completion rates, score distributions, and evaluator efficiency.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;