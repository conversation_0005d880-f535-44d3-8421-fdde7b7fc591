import React, { useState } from 'react';
import { 
  Save, 
  Send, 
  ArrowLeft,
  FileText,
  User,
  Calendar,
  Clock
} from 'lucide-react';
import { AnswerMark, Paper } from '../../types';
import PDFViewer from './PDFViewer';

interface EvaluationInterfaceProps {
  paper?: Paper;
  onBack?: () => void;
}

const EvaluationInterface: React.FC<EvaluationInterfaceProps> = ({ 
  paper,
  onBack 
}) => {
  const [marks, setMarks] = useState<AnswerMark[]>([
    { id: '1', questionNo: 1, marksGiven: 0, maxMarks: 20, comments: '' },
    { id: '2', questionNo: 2, marksGiven: 0, maxMarks: 25, comments: '' },
    { id: '3', questionNo: 3, marksGiven: 0, maxMarks: 25, comments: '' },
    { id: '4', questionNo: 4, marksGiven: 0, maxMarks: 30, comments: '' }
  ]);
  
  const [annotations, setAnnotations] = useState<any[]>([]);
  const [generalComments, setGeneralComments] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock paper data if not provided
  const currentPaper = paper || {
    id: '1',
    examId: '1',
    studentId: 'ST001',
    studentName: 'Alice Johnson',
    subject: 'Data Structures',
    uploadUrl: '/sample-paper.pdf',
    status: 'assigned' as const,
    assignedTo: '2',
    assignedEvaluator: 'Dr. Sarah Wilson',
    dateUploaded: '2024-03-16T10:00:00Z',
    dateAssigned: '2024-03-16T12:00:00Z',
    pageCount: 8
  };

  const updateMark = (questionNo: number, field: keyof AnswerMark, value: any) => {
    setMarks(prev => prev.map(mark => 
      mark.questionNo === questionNo 
        ? { ...mark, [field]: value }
        : mark
    ));
  };

  const getTotalMarks = () => {
    return marks.reduce((sum, mark) => sum + mark.marksGiven, 0);
  };

  const getMaxMarks = () => {
    return marks.reduce((sum, mark) => sum + mark.maxMarks, 0);
  };

  const handleSave = async () => {
    console.log('Saving evaluation progress...', {
      paperId: currentPaper.id,
      marks,
      annotations,
      generalComments
    });
    // Here you would save to backend
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      console.log('Submitting evaluation...', {
        paperId: currentPaper.id,
        marks,
        annotations,
        generalComments,
        totalMarks: getTotalMarks(),
        maxMarks: getMaxMarks()
      });
      // Here you would submit to backend
      alert('Evaluation submitted successfully!');
      if (onBack) onBack();
    } catch (error) {
      console.error('Error submitting evaluation:', error);
      alert('Error submitting evaluation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Left Sidebar - Paper Info & Marking */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={onBack}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              Back
            </button>
            <span className="text-sm text-gray-500">
              {currentPaper.status === 'assigned' ? 'In Progress' : 'Completed'}
            </span>
          </div>
          
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Paper Evaluation
          </h2>
          
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>{currentPaper.studentName} ({currentPaper.studentId})</span>
            </div>
            <div className="flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              <span>{currentPaper.subject}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Assigned: {new Date(currentPaper.dateAssigned!).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              <span>{currentPaper.pageCount} pages</span>
            </div>
          </div>
          
          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium text-blue-900">
              Total Score: {getTotalMarks()}/{getMaxMarks()}
            </div>
            <div className="text-xs text-blue-700 mt-1">
              {getMaxMarks() > 0 ? Math.round((getTotalMarks() / getMaxMarks()) * 100) : 0}%
            </div>
          </div>
        </div>

        {/* Marking Panel */}
        <div className="flex-1 overflow-auto p-4 space-y-4">
          {marks.map((mark) => (
            <div key={mark.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Question {mark.questionNo}</h4>
                <span className="text-sm text-gray-600">Max: {mark.maxMarks}</span>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Marks Awarded
                  </label>
                  <input
                    type="number"
                    min="0"
                    max={mark.maxMarks}
                    value={mark.marksGiven}
                    onChange={(e) => updateMark(mark.questionNo, 'marksGiven', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Comments
                  </label>
                  <textarea
                    value={mark.comments}
                    onChange={(e) => updateMark(mark.questionNo, 'comments', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Add feedback for the student..."
                  />
                </div>
              </div>
            </div>
          ))}

          {/* General Comments */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">General Comments</h4>
            <textarea
              value={generalComments}
              onChange={(e) => setGeneralComments(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Overall feedback for the student..."
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="p-4 border-t border-gray-200 space-y-2">
          <button 
            onClick={handleSave}
            className="w-full flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Progress
          </button>
          <button 
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <Send className="w-4 h-4 mr-2" />
            {isSubmitting ? 'Submitting...' : 'Submit Evaluation'}
          </button>
        </div>
      </div>

      {/* Right Side - PDF Viewer */}
      <div className="flex-1">
        <PDFViewer
          pdfUrl="/sample-paper.pdf"
          annotations={annotations}
          onAnnotationChange={setAnnotations}
        />
      </div>
    </div>
  );
};

export default EvaluationInterface;