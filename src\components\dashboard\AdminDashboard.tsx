import React from 'react';
import DashboardCard from './DashboardCard';
import { 
  FileText, 
  Users, 
  CheckCircle, 
  Clock,
  TrendingUp,
  AlertTriangle 
} from 'lucide-react';
import { mockDashboardStats, mockPapers, mockExams } from '../../services/mockData';

const AdminDashboard: React.FC = () => {
  const stats = mockDashboardStats.admin;
  
  const recentPapers = mockPapers.slice(0, 5);
  const activeExams = mockExams.filter(exam => exam.status === 'active');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleString()}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Total Papers"
          value={stats.totalPapers}
          icon={<FileText className="w-6 h-6 text-white" />}
          color="bg-blue-500"
          trend={{ value: 12, isPositive: true }}
        />
        <DashboardCard
          title="Completed"
          value={stats.completedPapers}
          icon={<CheckCircle className="w-6 h-6 text-white" />}
          color="bg-green-500"
          trend={{ value: 8, isPositive: true }}
        />
        <DashboardCard
          title="Pending"
          value={stats.pendingPapers}
          icon={<Clock className="w-6 h-6 text-white" />}
          color="bg-orange-500"
          trend={{ value: 5, isPositive: false }}
        />
        <DashboardCard
          title="Average Score"
          value={`${stats.averageScore}%`}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-purple-500"
          trend={{ value: 2.3, isPositive: true }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Exams */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Active Exams</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {activeExams.map((exam) => (
                <div key={exam.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">{exam.title}</h3>
                    <p className="text-sm text-gray-600">
                      {exam.subjects.length} subjects • {exam.totalPapers} papers
                    </p>
                  </div>
                  <div className="text-right">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Papers</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentPapers.map((paper) => (
                <div key={paper.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {paper.studentName}
                      </h4>
                      <p className="text-xs text-gray-600">{paper.subject}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      paper.status === 'completed' ? 'bg-green-100 text-green-800' :
                      paper.status === 'evaluated' ? 'bg-blue-100 text-blue-800' :
                      paper.status === 'assigned' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {paper.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* System Alerts */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">System Alerts</h2>
        </div>
        <div className="p-6">
          <div className="space-y-3">
            <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  15 papers pending evaluation for more than 24 hours
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  Consider reassigning to available evaluators
                </p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <CheckCircle className="w-5 h-5 text-blue-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-blue-800">
                  System backup completed successfully
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  Last backup: {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;