import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { 
  Home, 
  FileText, 
  Users, 
  CheckSquare, 
  BarChart3, 
  Settings,
  BookOpen,
  ClipboardCheck,
  Award
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { user } = useAuth();

  const getMenuItems = () => {
    const baseItems = [
      { icon: Home, label: 'Dashboard', path: '/' }
    ];

    switch (user?.role) {
      case 'admin':
        return [
          ...baseItems,
          { icon: FileText, label: 'Exam Management', path: '/exams' },
          { icon: BookOpen, label: 'Paper Management', path: '/papers' },
          { icon: Users, label: 'User Management', path: '/users' },
          { icon: ClipboardCheck, label: 'Evaluations', path: '/evaluations' },
          { icon: BarChart3, label: 'Reports', path: '/reports' },
          { icon: Settings, label: 'Settings', path: '/settings' }
        ];
      case 'evaluator':
        return [
          ...baseItems,
          { icon: BookOpen, label: 'My Papers', path: '/my-papers' },
          { icon: CheckSquare, label: 'Evaluate', path: '/evaluate' },
          { icon: BarChart3, label: 'My Performance', path: '/performance' }
        ];
      case 'auditor':
        return [
          ...baseItems,
          { icon: ClipboardCheck, label: 'Audit Queue', path: '/audit' },
          { icon: BookOpen, label: 'All Evaluations', path: '/all-evaluations' },
          { icon: BarChart3, label: 'Audit Reports', path: '/audit-reports' }
        ];
      case 'student':
        return [
          ...baseItems,
          { icon: Award, label: 'My Results', path: '/results' },
          { icon: FileText, label: 'My Papers', path: '/my-papers' }
        ];
      default:
        return baseItems;
    }
  };

  const menuItems = getMenuItems();

  return (
    <div className="bg-white w-64 min-h-screen shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">
          Digital Evaluation
        </h1>
        <p className="text-sm text-gray-600 capitalize">{user?.role} Portal</p>
      </div>
      
      <nav className="mt-6">
        {menuItems.map((item, index) => (
          <a
            key={index}
            href={item.path}
            className="flex items-center px-6 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
          >
            <item.icon className="w-5 h-5 mr-3" />
            <span className="font-medium">{item.label}</span>
          </a>
        ))}
      </nav>

      <div className="absolute bottom-6 left-6 right-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-sm">
                {user?.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-600">{user?.email}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;