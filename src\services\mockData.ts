import { Exam, Paper, Evaluation, AuditLog, DashboardStats } from '../types';

export const mockExams: Exam[] = [
  {
    id: '1',
    title: 'Computer Science Midterm 2024',
    startDate: '2024-03-15',
    endDate: '2024-03-20',
    subjects: ['Data Structures', 'Algorithms', 'Database Systems'],
    createdBy: '1',
    status: 'active',
    totalPapers: 150
  },
  {
    id: '2',
    title: 'Mathematics Final Exam 2024',
    startDate: '2024-05-10',
    endDate: '2024-05-15',
    subjects: ['Calculus', 'Linear Algebra', 'Statistics'],
    createdBy: '1',
    status: 'completed',
    totalPapers: 200
  }
];

export const mockPapers: Paper[] = [
  {
    id: '1',
    examId: '1',
    studentId: 'ST001',
    studentName: '<PERSON>',
    subject: 'Data Structures',
    uploadUrl: '/mock-papers/paper1.pdf',
    status: 'assigned',
    assignedTo: '2',
    assignedEvaluator: 'Dr. <PERSON>',
    dateUploaded: '2024-03-16T10:00:00Z',
    dateAssigned: '2024-03-16T12:00:00Z',
    pageCount: 8
  },
  {
    id: '2',
    examId: '1',
    studentId: 'ST002',
    studentName: 'Bob Smith',
    subject: 'Algorithms',
    uploadUrl: '/mock-papers/paper2.pdf',
    status: 'evaluated',
    assignedTo: '2',
    assignedEvaluator: 'Dr. Sarah Wilson',
    dateUploaded: '2024-03-16T10:15:00Z',
    dateAssigned: '2024-03-16T12:15:00Z',
    dateEvaluated: '2024-03-17T14:30:00Z',
    totalMarks: 85,
    pageCount: 6
  },
  {
    id: '3',
    examId: '1',
    studentId: 'ST003',
    studentName: 'Carol White',
    subject: 'Database Systems',
    uploadUrl: '/mock-papers/paper3.pdf',
    status: 'under-audit',
    assignedTo: '2',
    assignedEvaluator: 'Dr. Sarah Wilson',
    dateUploaded: '2024-03-16T10:30:00Z',
    dateAssigned: '2024-03-16T12:30:00Z',
    dateEvaluated: '2024-03-17T16:00:00Z',
    totalMarks: 92,
    pageCount: 10
  }
];

export const mockEvaluations: Evaluation[] = [
  {
    id: '1',
    paperId: '2',
    evaluatorId: '2',
    dateStarted: '2024-03-17T09:00:00Z',
    dateSubmitted: '2024-03-17T14:30:00Z',
    finalScore: 85,
    status: 'submitted',
    answers: [
      { id: '1', questionNo: 1, marksGiven: 18, maxMarks: 20, comments: 'Good approach, minor calculation error' },
      { id: '2', questionNo: 2, marksGiven: 25, maxMarks: 25, comments: 'Perfect solution' },
      { id: '3', questionNo: 3, marksGiven: 22, maxMarks: 25, comments: 'Correct logic, incomplete explanation' },
      { id: '4', questionNo: 4, marksGiven: 20, maxMarks: 30, comments: 'Needs more detailed analysis' }
    ],
    annotations: []
  }
];

export const mockAuditLogs: AuditLog[] = [
  {
    id: '1',
    action: 'Paper Assigned',
    entity: 'Paper',
    entityId: '1',
    userId: '1',
    userName: 'Admin User',
    timestamp: '2024-03-16T12:00:00Z',
    details: 'Paper assigned to Dr. Sarah Wilson'
  },
  {
    id: '2',
    action: 'Evaluation Completed',
    entity: 'Evaluation',
    entityId: '1',
    userId: '2',
    userName: 'Dr. Sarah Wilson',
    timestamp: '2024-03-17T14:30:00Z',
    details: 'Evaluation submitted with score 85/100'
  }
];

export const mockDashboardStats: Record<string, DashboardStats> = {
  admin: {
    totalPapers: 350,
    completedPapers: 245,
    pendingPapers: 105,
    averageScore: 78.5,
    evaluationSpeed: 2.3
  },
  evaluator: {
    totalPapers: 45,
    completedPapers: 32,
    pendingPapers: 13,
    averageScore: 76.8,
    evaluationSpeed: 1.8
  },
  auditor: {
    totalPapers: 78,
    completedPapers: 65,
    pendingPapers: 13,
    averageScore: 79.2,
    evaluationSpeed: 2.1
  }
};