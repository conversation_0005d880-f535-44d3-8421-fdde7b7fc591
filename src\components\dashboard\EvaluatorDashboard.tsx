import React from 'react';
import DashboardCard from './DashboardCard';
import { 
  FileText, 
  CheckCircle, 
  Clock,
  TrendingUp 
} from 'lucide-react';
import { mockDashboardStats, mockPapers } from '../../services/mockData';

const EvaluatorDashboard: React.FC = () => {
  const stats = mockDashboardStats.evaluator;
  const myPapers = mockPapers.filter(paper => paper.assignedTo === '2');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Evaluator Dashboard</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Start Evaluation
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Assigned Papers"
          value={stats.totalPapers}
          icon={<FileText className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <DashboardCard
          title="Completed"
          value={stats.completedPapers}
          icon={<CheckCircle className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <DashboardCard
          title="Pending"
          value={stats.pendingPapers}
          icon={<Clock className="w-6 h-6 text-white" />}
          color="bg-orange-500"
        />
        <DashboardCard
          title="Avg Score Given"
          value={`${stats.averageScore}%`}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Papers */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">My Assigned Papers</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {myPapers.map((paper) => (
                <div key={paper.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">{paper.studentName}</h3>
                    <p className="text-sm text-gray-600">
                      {paper.subject} • {paper.pageCount} pages
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Assigned: {new Date(paper.dateAssigned!).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      paper.status === 'evaluated' ? 'bg-green-100 text-green-800' :
                      paper.status === 'assigned' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {paper.status}
                    </span>
                    {paper.status === 'assigned' && (
                      <button className="block mt-2 text-xs text-blue-600 hover:text-blue-800">
                        Start Evaluation
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Performance Metrics</h2>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Completion Rate</span>
                  <span className="font-medium">71%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '71%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Average Time per Paper</span>
                  <span className="font-medium">1.8 hours</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Quality Score</span>
                  <span className="font-medium">4.7/5.0</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '94%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EvaluatorDashboard;