import React from 'react';
import DashboardCard from './DashboardCard';
import { 
  FileText, 
  Award, 
  Clock,
  TrendingUp,
  Calendar,
  Download
} from 'lucide-react';

const StudentDashboard: React.FC = () => {
  const studentResults = [
    {
      id: '1',
      subject: 'Data Structures',
      examDate: '2024-03-15',
      score: 85,
      maxScore: 100,
      grade: 'A',
      status: 'completed',
      evaluator: 'Dr. <PERSON>'
    },
    {
      id: '2',
      subject: 'Algorithms',
      examDate: '2024-03-18',
      score: 78,
      maxScore: 100,
      grade: 'B+',
      status: 'completed',
      evaluator: 'Prof. <PERSON>'
    },
    {
      id: '3',
      subject: 'Database Systems',
      examDate: '2024-03-20',
      score: 0,
      maxScore: 100,
      grade: '-',
      status: 'evaluating',
      evaluator: 'Dr. <PERSON>'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'evaluating': return 'bg-yellow-100 text-yellow-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600';
      case 'B+': case 'B': return 'text-blue-600';
      case 'C+': case 'C': return 'text-yellow-600';
      case 'D': case 'F': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const completedExams = studentResults.filter(r => r.status === 'completed');
  const averageScore = completedExams.length > 0 
    ? Math.round(completedExams.reduce((sum, r) => sum + r.score, 0) / completedExams.length)
    : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">My Results</h1>
        <div className="text-sm text-gray-500">
          Academic Year 2024-25
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Total Exams"
          value={studentResults.length}
          icon={<FileText className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <DashboardCard
          title="Completed"
          value={completedExams.length}
          icon={<Award className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <DashboardCard
          title="Pending"
          value={studentResults.filter(r => r.status !== 'completed').length}
          icon={<Clock className="w-6 h-6 text-white" />}
          color="bg-orange-500"
        />
        <DashboardCard
          title="Average Score"
          value={`${averageScore}%`}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Results Table */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Exam Results</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subject
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Grade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {studentResults.map((result) => (
                  <tr key={result.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 text-gray-400 mr-3" />
                        <div className="text-sm font-medium text-gray-900">
                          {result.subject}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-4 h-4 mr-1" />
                        {new Date(result.examDate).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {result.status === 'completed' ? `${result.score}/${result.maxScore}` : '-'}
                      </div>
                      {result.status === 'completed' && (
                        <div className="text-xs text-gray-500">
                          {Math.round((result.score / result.maxScore) * 100)}%
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-lg font-bold ${getGradeColor(result.grade)}`}>
                        {result.grade}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(result.status)}`}>
                        {result.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {result.status === 'completed' && (
                        <button className="text-blue-600 hover:text-blue-800 flex items-center">
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Summary</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Overall Performance</span>
                  <span className="font-medium">{averageScore}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${averageScore}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600">Completion Rate</span>
                  <span className="font-medium">
                    {Math.round((completedExams.length / studentResults.length) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full" 
                    style={{ width: `${(completedExams.length / studentResults.length) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-600">Algorithms exam evaluated</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span className="text-gray-600">Data Structures result published</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                <span className="text-gray-600">Database Systems under evaluation</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help?</h3>
            <p className="text-sm text-blue-700 mb-4">
              If you have questions about your results or need clarification on any evaluation, contact your instructor.
            </p>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              Contact Support →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;